package hk.org.ha.sc3.sybasechatops.scheduler;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import hk.org.ha.sc3.sybasechatops.config.SchedulerConfig;
import hk.org.ha.sc3.sybasechatops.service.RepserverService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class SchedulingRepserver {
    private final RepserverService repserverService;
    private final SchedulerConfig schedulerConfig;
    //private String apiToken;

    public SchedulingRepserver(RepserverService repserverService, SchedulerConfig schedulerConfig) {
        this.repserverService = repserverService;
        this.schedulerConfig = schedulerConfig;
    }

    @Scheduled(cron = "* * * * * *")
    public void schedulingUpdateRepserverData() {
        if (!schedulerConfig.isRepserverEnabled()) {
            log.info("Scheduling is disabled. Skipping scheduled task execution.");
            return;
        }

        log.info("Starting scheduled update chatops_non_db_product and chatops_repserver_dbsub");

        repserverService.updateAllNonDbProduct()
                .doOnNext(item -> log.info("Processing item: {}", item))
                .subscribe(result -> log.info("Update chatops_non_db_product Result: {}", result));
        log.info("Update chatops_non_db_product Result: {}", updateAllNonDbProductResult);

        String updateAllDbSubResult = repserverService.updateAllDbSub()
              .blockLast();
        log.info("Update chatops_repserver_dbsub Result: {}", updateAllDbSubResult);

    }

}